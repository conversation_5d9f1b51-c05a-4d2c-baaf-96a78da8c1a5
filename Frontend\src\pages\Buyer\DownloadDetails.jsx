import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { selectMyDownloads } from "../../redux/slices/buyerDashboardSlice";
import BuyerSidebar from "../../components/buyer/BuyerSidebar";
import { IoArrowBack } from "react-icons/io5";
import { FaPlay } from "react-icons/fa";
import mastercardLogo from "../../assets/images/mastercard-logo.png";
import "../../styles/BuyerAccount.css";
import "../../styles/DownloadDetails.css";

const getDownloadDetails = (downloadId, downloads) => {
  const foundDownload = downloads.find(download => download.id === downloadId);
  
  if (foundDownload) {
    return {
      ...foundDownload,
      orderId: `#${downloadId}245578`,
      date: foundDownload.downloadDate || "30 May 2024",
      time: "4:30PM",
      items: 1,
      totalAmount: "$22.00",
      customer: {
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "************"
      },
      payment: {
        method: "Mastercard",
        cardNumber: "**** **** **** 1234"
      },
      videoUrl: "https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=800&h=450&auto=format&fit=crop",
      description: "Learn Vortex - Drills and Coaching Philosophies to Developing Toughness in your Players to Win on the Court and in Life. Coach Vortex has been coaching for over 20 years and some advice for High School Coaches. Coach Vortex speaks on what does the building the Culture/Mindset that his Teams and Coaches for. Coach Vortex also does advice for College Coaches, where knowing him as favorite Drills when help teach a Game speed for toughness."
    };
  }
  
  return null;
};

const DownloadDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const downloads = useSelector(selectMyDownloads);
  const download = getDownloadDetails(id, downloads);

  if (!download) {
    return (
      <div className="BuyerAccount">
        <div className="container max-container">
          <div className="sidebar">
            <BuyerSidebar />
          </div>
          <div className="content">
            <div className="DownloadDetails__error">
              <p>Download not found</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="BuyerAccount">
      <div className="container max-container">
        <div className="sidebar">
          <BuyerSidebar />
        </div>
        <div className="content">
          <div className="DownloadDetails">
        {/* Header */}
        <div className="DownloadDetails__header">
          <button 
            className="DownloadDetails__back-btn"
            onClick={() => navigate(-1)}
          >
            <IoArrowBack />
            Back
          </button>
          <h1 className="DownloadDetails__title">
            Details of Order {download.orderId}
          </h1>
        </div>

        {/* Content Info */}
        <div className="DownloadDetails__content-info">
          <div className="DownloadDetails__content-image">
            <img
              src="https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
              alt={download.title}
            />
          </div>
          <div className="DownloadDetails__content-details">
            <h3 className="DownloadDetails__content-title">{download.title}</h3>
            <p className="DownloadDetails__content-coach">By {download.coach}</p>
          </div>
        </div>

        {/* Order Information */}
        <div className="DownloadDetails__order-info">
          <h3 className="DownloadDetails__section-title">Order Information</h3>
          <div className="DownloadDetails__info-grid">
            <div className="DownloadDetails__info-item">
              <span className="DownloadDetails__info-label">Order ID</span>
              <span className="DownloadDetails__info-value">{download.orderId}</span>
            </div>
            <div className="DownloadDetails__info-item">
              <span className="DownloadDetails__info-label">Items</span>
              <span className="DownloadDetails__info-value">{download.items}</span>
            </div>
            <div className="DownloadDetails__info-item">
              <span className="DownloadDetails__info-label">Date</span>
              <span className="DownloadDetails__info-value">{download.date} | {download.time}</span>
            </div>
            <div className="DownloadDetails__info-item">
              <span className="DownloadDetails__info-label">Total Amount</span>
              <span className="DownloadDetails__info-value">{download.totalAmount}</span>
            </div>
          </div>
        </div>

        {/* Customer and Payment Details */}
        <div className="DownloadDetails__details-grid">
          <div className="DownloadDetails__customer-details">
            <h3 className="DownloadDetails__section-title">Customer Details</h3>
            <div className="DownloadDetails__details-content">
              <div className="DownloadDetails__detail-item">
                <span className="DownloadDetails__detail-label">Name</span>
                <span className="DownloadDetails__detail-value">{download.customer.name}</span>
              </div>
              <div className="DownloadDetails__detail-item">
                <span className="DownloadDetails__detail-label">Email Address</span>
                <span className="DownloadDetails__detail-value">{download.customer.email}</span>
              </div>
              <div className="DownloadDetails__detail-item">
                <span className="DownloadDetails__detail-label">Phone Number</span>
                <span className="DownloadDetails__detail-value">{download.customer.phone}</span>
              </div>
            </div>
          </div>

          <div className="DownloadDetails__payment-details">
            <h3 className="DownloadDetails__section-title">Payment Details</h3>
            <div className="DownloadDetails__details-content">
              <div className="DownloadDetails__payment-method">
                <img
                  src={mastercardLogo}
                  alt="Mastercard"
                  className="DownloadDetails__payment-icon"
                />
                <span className="DownloadDetails__payment-text">{download.payment.cardNumber}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Video/Document Info */}
        <div className="DownloadDetails__video-section">
          <h3 className="DownloadDetails__section-title">Video/Document Info</h3>
          <div className="DownloadDetails__video-container">
            <div className="DownloadDetails__video-player">
              <img
                src={download.videoUrl}
                alt="Video thumbnail"
                className="DownloadDetails__video-thumbnail"
              />
              <div className="DownloadDetails__play-overlay">
                <button className="DownloadDetails__play-btn">
                  <FaPlay />
                </button>
              </div>
              <div className="DownloadDetails__video-title-overlay">
                <h4 className="DownloadDetails__video-title">{download.title}</h4>
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="DownloadDetails__description-section">
          <h3 className="DownloadDetails__section-title">Description</h3>
          <p className="DownloadDetails__description-text">
            {download.description}
          </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadDetails;
